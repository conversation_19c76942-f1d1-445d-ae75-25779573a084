"use client";

import { TimePeriod } from "@/types/enums";
import React from "react";

interface PeriodFilterProps {
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
}

const periods = [
  { label: 'All time', value: TimePeriod.ALL_TIME },
  { label: 'Yearly', value: TimePeriod.YEARLY },
];

const periodsSidebar = [
  { label: 'All time', value: TimePeriod.ALL_TIME },
  { label: 'Yearly', value: TimePeriod.YEARLY },
  { label: '6 Months', value: TimePeriod.SIX_MONTHS },
];

export function PeriodFilter({ 
  selectedPeriod, 
  onPeriodChange, 
}: PeriodFilterProps) {
  return (
    <div className="flex items-center space-x-6">
      {periods.map((period) => (
        <button
          key={period.value}
          onClick={() => onPeriodChange(period.value)}
          className={`relative pb-2 text-sm font-medium transition-colors
            ${selectedPeriod === period.value
              ? 'text-[#0E4DDB]'
              : 'text-muted-foreground hover:text-foreground'
            }`}
        >
          {period.label}
          {selectedPeriod === period.value && (
            <span className="absolute bottom-0 left-1/2 h-1.5 w-1.5 -translate-x-1/2 rounded-full bg-[#0E4DDB]" />
          )}
        </button>
      ))}
    </div>
  );
}

export function PeriodFilterSidebar({ 
  selectedPeriod, 
  onPeriodChange, 
}: PeriodFilterProps) {
  return (
    <div className="flex items-center space-x-6">
      {periodsSidebar.map((period) => (
        <button
          key={period.value}
          onClick={() => onPeriodChange(period.value)}
          className={`relative pb-2 text-sm font-medium transition-colors
            ${selectedPeriod === period.value
              ? 'text-[#0E4DDB]'
              : 'text-muted-foreground hover:text-foreground'
            }`}
        >
          {period.label}
          {selectedPeriod === period.value && (
            <span className="absolute bottom-0 left-1/2 h-1.5 w-1.5 -translate-x-1/2 rounded-full bg-[#0E4DDB]" />
          )}
        </button>
      ))}
    </div>
  );
}