"use client";

import { Chart<PERSON><PERSON>r, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { LineChart, Line, XAxis, YAxis } from "recharts";
import { PeriodFilterSidebar } from "./PeriodFilter";

interface PortfolioData {
  month: string;
  value: number;
}

interface PortfolioInsightsChartProps {
  data: PortfolioData[];
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  chartConfig: any;
}

export function PortfolioInsightsChart({ 
  data, 
  selectedPeriod, 
  onPeriodChange, 
  chartConfig 
}: PortfolioInsightsChartProps) {
  return (
    <div>
      <h3 className="font-semibold mb-4 text-gray-900">Portfolio insights</h3>
      
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Interest earned all time</span>
          <PeriodFilterSidebar 
            selectedPeriod={selectedPeriod}
            onPeriodChange={onPeriodChange}
          />
        </div>
      </div>

      <div className="h-48">
        <ChartContainer config={chartConfig} className="w-full h-full">
          <LineChart data={data}>
            <XAxis dataKey="month" axisLine={false} tickLine={false} />
            <YAxis axisLine={false} tickLine={false} />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line 
              dataKey="value" 
              stroke="#22c55e" 
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        </ChartContainer>
      </div>
    </div>
  );
}