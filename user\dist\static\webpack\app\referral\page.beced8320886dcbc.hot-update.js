"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/referral/page",{

/***/ "(app-pages-browser)/./src/components/referral/PortfolioInsightsChart.tsx":
/*!************************************************************!*\
  !*** ./src/components/referral/PortfolioInsightsChart.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PortfolioInsightsChart: () => (/* binding */ PortfolioInsightsChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _PeriodFilter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PeriodFilter */ \"(app-pages-browser)/./src/components/referral/PeriodFilter.tsx\");\n/* __next_internal_client_entry_do_not_use__ PortfolioInsightsChart auto */ \n\n\n\nfunction PortfolioInsightsChart(param) {\n    let { data, selectedPeriod, onPeriodChange, chartConfig } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"font-semibold mb-4 text-gray-900\",\n                children: \"Portfolio insights\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"Interest earned all time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PeriodFilter__WEBPACK_IMPORTED_MODULE_2__.PeriodFilterSidebar, {\n                            selectedPeriod: selectedPeriod,\n                            onPeriodChange: onPeriodChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-48\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_1__.ChartContainer, {\n                    config: chartConfig,\n                    className: \"w-full h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.LineChart, {\n                        data: data,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.XAxis, {\n                                dataKey: \"month\",\n                                axisLine: false,\n                                tickLine: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.YAxis, {\n                                axisLine: false,\n                                tickLine: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_1__.ChartTooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_1__.ChartTooltipContent, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 36\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Line, {\n                                dataKey: \"value\",\n                                stroke: \"#22c55e\",\n                                strokeWidth: 2,\n                                dot: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\referral\\\\PortfolioInsightsChart.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n_c = PortfolioInsightsChart;\nvar _c;\n$RefreshReg$(_c, \"PortfolioInsightsChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/referral/PortfolioInsightsChart.tsx\n"));

/***/ })

});